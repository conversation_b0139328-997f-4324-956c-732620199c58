<template>
  <view class="container">
    <div class="main">
      <view class="section">
        <view class="section-title">业务类型</view>
        <view class="business-container">
          <view
            v-for="(item, index) in business"
            :key="index"
            :class="[
              'business-item',
              { active: selectedBusiness === item.value },
            ]"
            @click="selectBusiness(item.value)"
          >
            {{ item.name }}
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">气瓶规格</view>
        <view class="specs-container">
          <view
            v-for="(item, index) in (specs || [])"
            :key="index"
            :class="['spec-item', { active: (item.quantity || 0) > 0 }]"
          >
            <image
              :src="item.image"
              mode="aspectFit"
              class="spec-image"
            ></image>
            <view class="spec-info">
              <view class="spec-name">{{ item.name }}</view>
              <view class="spec-net-weight">净重{{ item.netWeight }}</view>
              <view class="spec-price">¥{{ item.price }}</view>
            </view>
            <view class="quantity-control">
              <view class="quantity-btn" @click="decrementQuantity(index)">-</view>
              <view class="quantity-value">{{ item.quantity || 0 }}</view>
              <view class="quantity-btn" @click="incrementQuantity(index)">+</view>
            </view>
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">配送地址</view>
        <view class="store-info">
          <view class="store-details">
            <view class="store-contact-info">
              <view class="contact-item">
                <text class="contact-label">联系人：</text>
                <text class="contact-value">{{ storeInfo.contactName }}</text>
              </view>
              <view class="contact-item">
                <text class="contact-label">联系电话：</text>
                <text class="contact-value phone">{{ storeInfo.phone }}</text>
              </view>
            </view>
            <view class="store-address">
              <text class="address-label">配送地址：</text>
              <text class="address-value">{{ storeInfo.address }}</text>
            </view>
          </view>
          <view class="store-map">
            <image :src="storeInfo.mapImageUrl" mode="aspectFill" class="map-image"></image>
          </view>
        </view>
      </view>

      <!-- 配送时间选择 -->
      <view class="section" v-if="selectedBusiness === 2">
        <TimeSelector
          title="配送时间"
          :dates="deliveryDates"
          :time-slots="availableTimeSlots"
          :selected-date-index="selectedDateIndex"
          :selected-time-index="selectedTimeIndex"
          @date-change="handleDeliveryDateChange"
          @time-change="handleDeliveryTimeChange"
        />
      </view>

      <view class="section" v-if="selectedBusiness === 1">
        <view class="section-title">{{ alarmName }}</view>
        <view class="specs-container">
          <view :class="['spec-item', { active: alarmQuantity > 0 }]">
            <image
              src="/static/images/alarm.jpg"
              mode="aspectFit"
              class="spec-image"
            ></image>
            <view class="spec-info">
              <view class="spec-name">{{ alarmName }}</view>
              <view class="spec-net-weight">根据安全规定，每4个气瓶需绑定1个报警器</view>
              <view class="spec-price">¥{{ alarmRentalPrice }}/个<text class="deposit-text">(押金)</text></view>
            </view>
            <view class="quantity-control">
              <view class="quantity-btn" @click.stop="decrementAlarmQuantity">-</view>
              <view class="quantity-value">{{ alarmQuantity }}</view>
              <view class="quantity-btn" @click.stop="incrementAlarmQuantity">+</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 预约安装时间选择 -->
      <view class="section" v-if="selectedBusiness === 1 && alarmQuantity > 0">
        <TimeSelector
          title="预约安装时间"
          :dates="deliveryDates"
          :time-slots="installationTimeSlots"
          :selected-date-index="installationDateIndex"
          :selected-time-index="installationTimeIndex"
          @date-change="handleInstallationDateChange"
          @time-change="handleInstallationTimeChange"
        />
      </view>

      <!-- 订单总览和详情 -->
      <view class="section order-section">
        <view class="section-title">订单总览</view>
        
        <!-- 订单信息卡片 -->
        <view class="order-card">
          <view class="order-card-header">
            <text class="card-title">订单信息</text>
          </view>
          <view class="order-card-body">
            <view class="info-row">
              <text class="info-label">气瓶总数</text>
              <text class="info-value">{{ totalBottles }}个</text>
            </view>
            <view class="info-row">
              <text class="info-label">联系人</text>
              <text class="info-value">{{ storeInfo.contactName }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">联系电话</text>
              <text class="info-value">{{ storeInfo.phone }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">配送地址</text>
              <text class="info-value">{{ storeInfo.address }}</text>
            </view>
            <view class="info-row" v-if="selectedBusiness === 2 && deliveryDates[selectedDateIndex]">
              <text class="info-label">配送时间</text>
              <text class="info-value">{{ deliveryDates[selectedDateIndex].week }} {{ availableTimeSlots[selectedTimeIndex]?.text || '' }}</text>
            </view>
            <view class="info-row" v-if="selectedBusiness === 1 && alarmQuantity > 0">
              <text class="info-label">{{ alarmName }}<text class="deposit-text">(押金)</text></text>
              <text class="info-value">¥{{ alarmPrice }}</text>
            </view>
            <view class="info-row" v-if="selectedBusiness === 1 && alarmQuantity > 0 && deliveryDates[installationDateIndex]">
              <text class="info-label">预约安装时间</text>
              <text class="info-value">{{ deliveryDates[installationDateIndex].week }} {{ installationTimeSlots[installationTimeIndex]?.text || '' }}</text>
            </view>
          </view>
        </view>
        
        <!-- 商品明细卡片 -->
        <view class="order-card" v-if="totalBottles > 0 || alarmQuantity > 0">
          <view class="order-card-header">
            <text class="card-title">商品明细</text>
          </view>
          <view class="order-card-body">
            <view 
              v-for="(item, index) in (specs || [])" 
              :key="index" 
              :class="['item-row', {'inactive-item': (item.quantity || 0) === 0}]"
            >
              <view class="item-info">
                <text class="item-name">{{ item.name }}</text>
                <text class="item-net-weight">净重{{ item.netWeight }}</text>
                <text class="item-unit">¥{{ item.price }}/个</text>
              </view>
              <view class="item-summary">
                <text class="item-quantity">x {{ item.quantity || 0 }}</text>
                <text class="item-total">¥{{ (item.price * (item.quantity || 0)).toFixed(2) }}</text>
              </view>
            </view>
            
            <view class="item-row alarm-row" v-if="selectedBusiness === 1 && alarmQuantity > 0">
              <view class="item-info">
                <text class="item-name">{{ alarmName }}</text>
                <text class="item-unit">¥{{ alarmRentalPrice }}/个<text class="deposit-text">(押金)</text></text>
              </view>
              <view class="item-summary">
                <text class="item-quantity">x {{ alarmQuantity }}</text>
                <text class="item-total">¥{{ alarmPrice }}</text>
              </view>
            </view>
            <view class="item-row" v-if="selectedBusiness === 2 && availableTimeSlots[selectedTimeIndex]?.extraFee > 0">
              <view class="item-info">
                <text class="item-name">立即配送费</text>
                <text class="item-unit">配送费</text>
              </view>
              <view class="item-summary">
                <text class="item-quantity">x 1</text>
                <text class="item-total">¥{{ availableTimeSlots[selectedTimeIndex]?.extraFee }}</text>
              </view>
            </view>
            
            <view class="order-divider"></view>
            
            <view class="total-row">
              <text class="total-label">合计</text>
              <text class="total-value">¥{{ totalPrice }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 服务协议 -->
      <view class="section agreement-section">
        <label class="agreement-checkbox">
          <checkbox :checked="agreementChecked" @tap="toggleAgreement" color="var(--primary-color)" />
          <text class="agreement-text">
            我已阅读并同意
            <text class="agreement-link" @tap.stop="showAgreementDetails">《服务协议》</text>
          </text>
        </label>
      </view>
    </div>
    
    <view class="submit-bar">
      <view class="total">
        <text>合计：</text>
        <text class="price">¥{{ totalPrice }}</text>
      </view>
      <button class="submit-btn" :disabled="!canSubmit" @click="submitOrder">
        提交订单
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import TimeSelector from '@/components/TimeSelector.vue'

// 响应式数据
const business = ref([
  { name: "换气", value: 2 },
  { name: "租赁", value: 1 },
])

const selectedBusiness = ref(2) // 默认选中换气
const specs = ref([
  {
    id: 1,
    name: "5kg液化气瓶",
    netWeight: "4kg",
    price: 70,
    image: "/static/images/5kg.jpg",
    quantity: 0
  },
  {
    id: 2,
    name: "15kg液化气瓶",
    netWeight: "12.5kg",
    price: 130,
    image: "/static/images/15kg.jpg",
    quantity: 0
  },
  {
    id: 3,
    name: "50kg液化气瓶",
    netWeight: "45kg",
    price: 380,
    image: "/static/images/50kg.jpg",
    quantity: 0
  },
])

const storeInfo = ref({
  contactName: "张师傅",
  phone: "************",
  address: "广州市海珠区新港东路123号",
  latitude: 23.106574,
  longitude: 113.324520,
  mapImageUrl: "/static/images/store-map.png" // 预先准备的地图图片
})

const deliveryTimes = ref(["9:00-12:00", "14:00-18:00", "18:00-21:00"])
const deliveryDates = ref([])
const selectedDateIndex = ref(0)
const selectedTimeIndex = ref(0)
const immediateDeliveryFee = ref(10) // 立即配送费用
const alarmQuantity = ref(0)        // 报警器数量
const alarmRentalPrice = ref(100)   // 报警器押金单价
const currentBottles = ref(3) // 当前用户已有气瓶数量，从服务端获取
const userType = ref('resident') // 用户类型：resident(居民), mobile(移动), business-enterprise(店铺/企业)
// 预约安装时间相关
const installationDateIndex = ref(0) // 预约安装日期索引
const installationTimeIndex = ref(0) // 预约安装时间索引
const agreementChecked = ref(false)

// 计算属性
// 总气瓶数量
const totalBottles = computed(() => {
  return specs.value.reduce((sum, spec) => sum + (spec.quantity || 0), 0)
})

// 是否需要报警器
const needAlarm = computed(() => {
  return (currentBottles.value + totalBottles.value) % 4 !== 0
})

// 报警器价格
const alarmPrice = computed(() => {
  return alarmQuantity.value * alarmRentalPrice.value
})

// 报警器名称（根据用户类型）
const alarmName = computed(() => {
  if (userType.value === 'business-enterprise') {
    return '商用燃气报警器'
  } else {
    // resident 或 mobile 用户都使用家用燃气报警器
    return '家用燃气报警器'
  }
})

// 气瓶总价格
const specPrice = computed(() => {
  return specs.value.reduce((sum, spec) => sum + ((spec.quantity || 0) * spec.price), 0)
})

// 总价格
const totalPrice = computed(() => {
  let total = specPrice.value

  // 添加报警器押金费用
  if (selectedBusiness.value === 1 && alarmQuantity.value > 0) {
    total += alarmPrice.value
  }

  // 添加立即配送费用 - 只在换气业务时添加
  if (selectedBusiness.value === 2) {
    const selectedTimeSlot = availableTimeSlots.value[selectedTimeIndex.value]
    if (selectedTimeSlot && selectedTimeSlot.extraFee > 0) {
      total += selectedTimeSlot.extraFee
    }
  }

  return total
})

// 可用时间段
const availableTimeSlots = computed(() => {
  if (selectedDateIndex.value === null || !deliveryDates.value[selectedDateIndex.value]) {
    return []
  }

  const selectedDate = deliveryDates.value[selectedDateIndex.value]
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  today.setHours(0, 0, 0, 0)

  let timeSlots = []

  // 只有当天才显示立即配送选项
  if (selectedDate.date.getTime() === today.getTime()) {
    timeSlots.push({
      text: '立即配送',
      available: true,
      extraFee: immediateDeliveryFee.value
    })
  }

  // 添加固定时间段
  deliveryTimes.value.forEach(timeRange => {
    const [startTime] = timeRange.split('-')
    const [startHour, startMinute] = startTime.split(':').map(Number)

    const slotDateTime = new Date(selectedDate.date)
    slotDateTime.setHours(startHour, startMinute, 0, 0)

    // 如果当前日期是今天，且时间段已过，则不可用
    let available = true
    if (selectedDate.date.getTime() === today.getTime() && now > slotDateTime) {
      available = false
    }

    timeSlots.push({
      text: timeRange,
      available: available,
      extraFee: 0
    })
  })

  return timeSlots
})

// 预约安装可用时间段
const installationTimeSlots = computed(() => {
  if (installationDateIndex.value === null || !deliveryDates.value[installationDateIndex.value]) {
    return []
  }

  // 预约安装时间段（不包含立即配送选项）
  let timeSlots = []
  deliveryTimes.value.forEach(timeRange => {
    timeSlots.push({
      text: timeRange,
      available: true,
      extraFee: 0
    })
  })

  return timeSlots
})

// 是否可以提交订单
const canSubmit = computed(() => {
  if (totalBottles.value === 0) return false
  if (!agreementChecked.value) return false

  if (selectedBusiness.value === 2) {
    // 换气业务需要选择配送时间
    return selectedDateIndex.value !== null && selectedTimeIndex.value !== null
  } else {
    // 租赁业务不需要配送时间
    return true
  }
})

// 页面加载时的处理
onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options

  // 从页面参数中获取用户类型
  if (options.userType) {
    userType.value = options.userType
  }

  // 获取用户气瓶数量
  getCurrentBottles()
  // 初始化规格的quantity属性，确保响应式
  specs.value.forEach((spec, index) => {
    if (typeof spec.quantity === 'undefined') {
      specs.value[index].quantity = 0
    }
  })
  // 初始化配送日期
  initDeliveryDates()
  selectedDateIndex.value = 0 // 默认选中今天
  selectedTimeIndex.value = 0 // 默认选中第一个可用时间
  // 这里可以添加获取店铺信息的API调用
})

// 方法
// 选择业务类型
const selectBusiness = (value) => {
  selectedBusiness.value = value

  // 重置配送时间选择
  if (value === 1) {
    // 租赁模式不需要配送时间
    selectedDateIndex.value = null
    selectedTimeIndex.value = null
  } else {
    // 换气模式需要配送时间，默认选择第一个
    selectedDateIndex.value = 0
    selectedTimeIndex.value = 0
  }
}

// 增加数量
const incrementQuantity = (index) => {
  const newQuantity = (specs.value[index].quantity || 0) + 1
  specs.value[index].quantity = newQuantity
}

// 减少数量
const decrementQuantity = (index) => {
  if (specs.value[index].quantity > 0) {
    const newQuantity = specs.value[index].quantity - 1
    specs.value[index].quantity = newQuantity
  }
}

// 增加报警器数量
const incrementAlarmQuantity = () => {
  alarmQuantity.value++
}

// 减少报警器数量
const decrementAlarmQuantity = () => {
  if (alarmQuantity.value > 0) {
    alarmQuantity.value--
  }
}

// 获取用户当前气瓶数量
const getCurrentBottles = () => {
  // 实际应用中应该调用API获取
  // 这里模拟API调用
  setTimeout(() => {
    // 模拟数据，实际应从服务器获取
    currentBottles.value = 3
  }, 500)
}

// 提交订单
const submitOrder = () => {
  if (!canSubmit.value) return

  uni.showLoading({
    title: "提交中",
  })

  // 构建订单数据
  const orderData = {
    businessType: selectedBusiness.value,
    specs: specs.value.filter(spec => (spec.quantity || 0) > 0).map(spec => ({
      id: spec.id,
      name: spec.name,
      netWeight: spec.netWeight,
      quantity: spec.quantity,
      price: spec.price
    })),
    storeInfo: storeInfo.value
  }

  // 只有换气业务才有配送时间
  if (selectedBusiness.value === 2) {
    const selectedDate = deliveryDates.value[selectedDateIndex.value]
    const selectedTimeSlot = availableTimeSlots.value[selectedTimeIndex.value]

    orderData.delivery = {
      date: selectedDate,
      time: selectedTimeSlot.text,
      extraFee: selectedTimeSlot.extraFee || 0
    }
  }

  // 只有租赁业务才有报警器押金
  if (selectedBusiness.value === 1 && alarmQuantity.value > 0) {
    orderData.alarmDeposit = {
      quantity: alarmQuantity.value,
      price: alarmRentalPrice.value
    }

    // 添加预约安装时间信息
    const installationDate = deliveryDates.value[installationDateIndex.value]
    const installationTime = installationTimeSlots.value[installationTimeIndex.value]
    if (installationDate && installationTime) {
      orderData.installationAppointment = {
        date: installationDate.date,
        week: installationDate.week,
        time: installationTime.text,
        extraFee: installationTime.extraFee || 0
      }
    }
  }

  orderData.totalAmount = totalPrice.value

  // 模拟API调用
  setTimeout(() => {
    uni.hideLoading()

    // 模拟订单提交成功
    const orderId = "GAS" + Date.now()

    // 调用支付
    requestPayment(orderId, totalPrice.value)
  }, 1000)
}

// 初始化配送日期
const initDeliveryDates = () => {
  const dates = []
  const now = new Date()
  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

  for (let i = 0; i < 7; i++) {
    const date = new Date(now)
    date.setHours(0, 0, 0, 0) // 设置为当天的开始时间
    date.setDate(date.getDate() + i)

    dates.push({
      date: date,
      week: i === 0 ? '今天' : weekDays[date.getDay()],
      day: `${date.getMonth() + 1}/${date.getDate()}`
    })
  }

  deliveryDates.value = dates
}

// 配送时间选择事件处理
const handleDeliveryDateChange = (index) => {
  selectedDateIndex.value = index
  selectedTimeIndex.value = 0 // 重置时间段选择
}

const handleDeliveryTimeChange = (index) => {
  selectedTimeIndex.value = index
}

// 预约安装时间选择事件处理
const handleInstallationDateChange = (index) => {
  installationDateIndex.value = index
  installationTimeIndex.value = 0 // 重置时间段选择
}

const handleInstallationTimeChange = (index) => {
  installationTimeIndex.value = index
}

// 选择配送日期（保留兼容性）
const selectDate = (index) => {
  selectedDateIndex.value = index
  selectedTimeIndex.value = 0 // 重置时间段选择
}

// 选择配送时间（保留兼容性）
const selectTime = (index, available) => {
  if (!available) return
  selectedTimeIndex.value = index
}

// 切换协议勾选状态
const toggleAgreement = () => {
  agreementChecked.value = !agreementChecked.value
}

// 显示协议详情
const showAgreementDetails = () => {
  let content = selectedBusiness.value === 2
    ? '换气业务协议：\n1. 用户提交订单后，平台将安排配送人员按照约定时间进行配送。\n2. 用户需确保配送地址准确，并在约定时间有人接收。\n3. 平台将提供优质的配送服务，确保气瓶安全送达。'
    : '租赁业务协议：\n1. 用户提交订单后，平台将安排工作人员按照约定进行服务。\n2. 报警器需按照安全规定正确使用，用户需对租赁设备妥善保管。\n3. 平台将提供专业的安装和维护服务。'

  uni.showModal({
    title: '服务协议',
    content: content,
    confirmText: '我已阅读',
    success: (res) => {
      if (res.confirm) {
        agreementChecked.value = true
      }
    }
  })
}

// 发起支付
const requestPayment = (orderId, amount) => {
  // 实际应用中，应调用服务端接口获取支付参数
  uni.showModal({
    title: "模拟支付",
    content: `订单金额：¥${amount}，是否确认支付？`,
    success: (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: "支付中",
        })

        // 模拟支付过程
        setTimeout(() => {
          uni.hideLoading()

          uni.showToast({
            title: "支付成功",
            icon: "success",
          })

          // 跳转到订单详情页
          setTimeout(() => {
            uni.redirectTo({
              url: `/pages/order/detail?id=${orderId}`,
            })
          }, 1500)
        }, 1000)
      }
    },
  })
}
</script>

<style scoped>
page {
  height: 100%;
  background-color: #f8f9fa;
}

.container {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  position: relative;
}

.container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background-color: #f8f9fa;
  z-index: -1;
}

.main {
  padding: 32rpx 24rpx 160rpx;
  flex: 1;
  overflow: auto;
  position: relative;
  z-index: 1;
}
.section {
  margin-bottom: 30rpx;
  background-color: #fff;
  padding: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  border-radius: 20rpx;
}

.section-title {
  line-height: 1;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  padding-left: 10rpx;
  border-left: 8rpx solid var(--primary-color);
}





.time-price {
  font-size: 20rpx;
  color: #ff6b6b;
  margin-top: 4rpx;
}

.time-item.active .time-price {
  color: #fff;
}

/* 业务类型样式 */
.business-container {
  display: flex;
  margin-bottom: 20rpx;
}

.business-item {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  margin: 0 10rpx;
  border-radius: 8rpx;
  border: 2rpx solid transparent;
}

.business-item.active {
  border-color: var(--primary-color);
  background-color: rgba(41, 121, 255, 0.05);
  color: var(--primary-color);
}



/* 规格样式 */
.specs-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.spec-item {
  display: flex;
  padding: 24rpx;
  border-radius: 16rpx;
  background-color: #ffffff;
  border: 2rpx solid #f0f0f0;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.spec-item:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  border-color: #e0e0e0;
}

.spec-item.active {
  border-color: var(--primary-color);
  background-color: rgba(41, 121, 255, 0.02);
  box-shadow: 0 4rpx 16rpx rgba(41, 121, 255, 0.15);
}

/* 数量选择器样式 */
.quantity-control {
  display: flex;
  align-items: center;
  margin-left: auto;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  overflow: hidden;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  background-color: #f8f9fa;
  color: #666;
  border: none;
  transition: all 0.2s ease;
  cursor: pointer;
  user-select: none;
}

.quantity-btn:active {
  background-color: var(--primary-color);
  color: #ffffff;
  transform: scale(0.95);
}







.quantity-value {
  width: 80rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  background-color: #ffffff;
  color: var(--primary-color);
  border-left: 1rpx solid #e9ecef;
  border-right: 1rpx solid #e9ecef;
}

.spec-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background-color: #fff;
  border: 1rpx solid #e9ecef;
}

.spec-info {
  flex: 1;
}

.spec-name {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
  color: #333;
  line-height: 1.4;
}

.spec-net-weight {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.spec-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.spec-price {
  font-size: 32rpx;
  color: #ff6700;
  font-weight: 700;
}

.deposit-text {
  font-size: 24rpx;
  color: #999;
  font-weight: 400;
}

/* 配送地址信息样式 */
.store-info {
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.store-details {
  margin-bottom: 20rpx;
}

.store-contact-info {
  margin-bottom: 16rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 28rpx;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-label {
  color: #666;
  min-width: 120rpx;
}

.contact-value {
  color: #333;
  font-weight: 500;
}

.phone {
  color: var(--primary-color);
}

.store-address {
  display: flex;
  align-items: flex-start;
  font-size: 28rpx;
  margin-bottom: 15rpx;
}

.address-label {
  color: #666;
  min-width: 120rpx;
  flex-shrink: 0;
}

.address-value {
  color: #333;
  font-weight: 500;
  line-height: 1.4;
}

.store-map {
  width: 100%;
  height: 300rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.map-image {
  width: 100%;
  height: 100%;
}



/* 订单详情与总览 */
.order-section {
  margin-bottom: 60rpx;
}

.order-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.order-card:last-child {
  margin-bottom: 0;
}

.order-card-header {
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #f9f9f9;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.order-card-body {
  padding: 24rpx;
}

/* 订单信息部分 */
.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  align-items: center;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #666;
}

.info-value {
  color: #333;
  font-weight: 500;
  max-width: 380rpx;
  text-align: right;
  word-break: break-all;
}

/* 商品明细部分 */
.item-row {
  display: flex;
  justify-content: space-between;
  padding: 16rpx 0;
}

.inactive-item {
  display: none;
}

.item-info {
  display: flex;
  flex-direction: column;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.item-net-weight {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.item-unit {
  font-size: 24rpx;
  color: #999;
}

.item-summary {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
}

.item-quantity {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.item-total {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.order-divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 20rpx 0;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 32rpx;
}

.total-label {
  color: #333;
  font-weight: bold;
}

.total-value {
  color: #ff6700;
  font-size: 36rpx;
  font-weight: bold;
}

/* 服务协议 */
.agreement-section {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f0f0f0;
}

.agreement-checkbox {
  display: flex;
  align-items: center;
}

.agreement-text {
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}

.agreement-link {
  color: var(--primary-color);
}

/* 提交栏 */
.submit-bar {
  z-index: 2;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.total {
  flex: 1;
  font-size: 28rpx;
}

.price {
  color: #ff6700;
  font-size: 36rpx;
  font-weight: bold;
}

.submit-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
}
.submit-btn::after {
  border: none;
}
</style>
