<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>退瓶确认页面测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f8f8f8;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 20px;
        }
        .card {
            background: #f9f9f9;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .card-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
        }
        .label {
            color: #666;
        }
        .value {
            font-weight: 500;
        }
        .highlight {
            color: #2979ff;
            font-weight: bold;
        }
        .positive {
            color: #19be6b;
        }
        .device-type-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 15px;
            color: white;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .device-type-badge.cylinder {
            background-color: #2979ff;
        }
        .device-type-badge.alarm {
            background-color: #fa3534;
        }
        .test-buttons {
            margin-top: 20px;
        }
        .test-button {
            background: #2979ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">退瓶确认页面测试</div>
        <div class="subtitle">测试移除租金功能和报警器类型</div>
        
        <div class="test-buttons">
            <button class="test-button" onclick="testCylinder()">测试气瓶</button>
            <button class="test-button" onclick="testAlarm()">测试报警器</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        // 报警器用户类型数据
        const alarmUserTypes = [
            { name: '居民用户', value: 'resident' },
            { name: '移动用户', value: 'mobile' },
            { name: '店铺/企业用户', value: 'business-enterprise' }
        ];

        function getAlarmUserTypeName(value) {
            const userType = alarmUserTypes.find(type => type.value === value);
            return userType ? userType.name : '未知类型';
        }

        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        function testCylinder() {
            const deviceInfo = {
                type: 'cylinder',
                id: 'CYL001',
                modelName: '液化石油气钢瓶',
                specification: '15kg',
                borrowDate: '2024-06-01',
                borrowDays: 45,
                deposit: 100,
                status: '正常'
            };

            displayResult(deviceInfo);
        }

        function testAlarm() {
            const deviceInfo = {
                type: 'alarm',
                id: 'ALM001',
                modelName: '家用燃气报警器',
                borrowDate: '2024-05-15',
                borrowDays: 75,
                deposit: 50,
                status: '正常',
                userType: 'resident'
            };

            displayResult(deviceInfo);
        }

        function displayResult(deviceInfo) {
            const resultDiv = document.getElementById('result');
            
            let html = `
                <div class="device-type-badge ${deviceInfo.type}">
                    ${deviceInfo.type === 'cylinder' ? '气瓶' : '报警器'}
                </div>
                
                <div class="card">
                    <div class="card-title">设备信息</div>
                    <div class="info-item">
                        <span class="label">设备编号</span>
                        <span class="value">${deviceInfo.id}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">设备类型</span>
                        <span class="value">${deviceInfo.modelName}</span>
                    </div>
                    ${deviceInfo.specification ? `
                    <div class="info-item">
                        <span class="label">规格</span>
                        <span class="value highlight">${deviceInfo.specification}</span>
                    </div>
                    ` : ''}
                    <div class="info-item">
                        <span class="label">状态</span>
                        <span class="value">${deviceInfo.status}</span>
                    </div>
                </div>

                <div class="card">
                    <div class="card-title">使用信息</div>
                    <div class="info-item">
                        <span class="label">开始日期</span>
                        <span class="value">${deviceInfo.borrowDate}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">已使用</span>
                        <span class="value highlight">${deviceInfo.borrowDays}天</span>
                    </div>
                    ${deviceInfo.type === 'alarm' ? `
                    <div class="info-item">
                        <span class="label">用户类型</span>
                        <span class="value">${getAlarmUserTypeName(deviceInfo.userType)}</span>
                    </div>
                    ` : ''}
                    <div class="info-item">
                        <span class="label">今日归还</span>
                        <span class="value">${formatDate(new Date())}</span>
                    </div>
                </div>

                ${deviceInfo.deposit > 0 ? `
                <div class="card">
                    <div class="card-title">押金信息</div>
                    <div class="info-item">
                        <span class="label">押金金额</span>
                        <span class="value positive">¥${deviceInfo.deposit.toFixed(2)}</span>
                    </div>
                </div>
                ` : ''}
            `;

            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
