<template>
  <view class="container">
    <view class="page-header">
      <view class="title">设备归还确认</view>
      <view class="subtitle">请确认以下信息无误</view>
    </view>

    <view class="loading-container" v-if="loading">
      <uni-icons type="spinner-cycle" size="40" color="#2979ff"></uni-icons>
      <text class="loading-text">正在加载信息...</text>
    </view>

    <view class="error-container" v-if="error">
      <uni-icons type="error" size="80" color="#fa3534"></uni-icons>
      <text class="error-text">{{ errorMsg }}</text>
      <button class="action-button primary" @click="scanAgain">重新扫码</button>
    </view>

    <view class="info-container" v-if="!loading && !error && deviceInfo">
      <view class="device-type-badge" :class="deviceInfo.type === 'cylinder' ? 'cylinder' : 'alarm'">
        <uni-icons :type="deviceInfo.type === 'cylinder' ? 'download' : 'notification'" size="18" color="#ffffff"></uni-icons>
        <text>{{ deviceInfo.type === 'cylinder' ? '气瓶' : '报警器' }}</text>
      </view>
      
      <!-- 设备基本信息卡片 -->
      <view class="card">
        <view class="card-title">设备信息</view>
        
        <view class="info-grid">
          <view class="info-item">
            <text class="label">设备编号</text>
            <text class="value">{{ deviceInfo.id }}</text>
          </view>
          
          <view class="info-item">
            <text class="label">设备类型</text>
            <text class="value">{{ deviceInfo.modelName }}</text>
          </view>
          
          <view class="info-item" v-if="deviceInfo.type === 'cylinder'">
            <text class="label">规格</text>
            <text class="value highlight">{{ deviceInfo.specification }}</text>
          </view>
          
          <view class="info-item">
            <text class="label">状态</text>
            <view class="status-tag" :class="deviceInfo.status === '正常' ? 'normal' : 'warning'">
              {{ deviceInfo.status }}
            </view>
          </view>
        </view>
      </view>

      <!-- 使用信息卡片 -->
      <view class="card">
        <view class="card-title">使用信息</view>

        <view class="info-grid">
          <view class="info-item">
            <text class="label">开始日期</text>
            <text class="value">{{ deviceInfo.startDate }}</text>
          </view>

          <view class="info-item">
            <text class="label">已使用</text>
            <text class="value highlight">{{ deviceInfo.usageDays }}天</text>
          </view>

          <view class="info-item" v-if="deviceInfo.type === 'alarm'">
            <text class="label">用户类型</text>
            <text class="value">{{ getAlarmUserTypeName(deviceInfo.userType) }}</text>
          </view>

          <view class="info-item">
            <text class="label">今日归还</text>
            <text class="value">{{ formatDate(new Date()) }}</text>
          </view>
        </view>
      </view>

      <view class="action-buttons">
        <button class="action-button cancel" @click="cancel">取消</button>
        <button class="action-button primary" @click="confirmReturn">确认归还</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      code: '',
      loading: true,
      error: false,
      errorMsg: '',
      deviceInfo: null,
      alarmUserTypes: [
        { name: '居民用户', value: 'resident' },
        { name: '移动用户', value: 'mobile' },
        { name: '店铺/企业用户', value: 'business-enterprise' }
      ]
    }
  },

  onLoad(options) {
    if (options.code) {
      this.code = decodeURIComponent(options.code)
      this.fetchDeviceInfo()
    } else {
      this.error = true
      this.loading = false
      this.errorMsg = '未获取到设备编码，请重新扫码'
    }
  },

  methods: {
    // 获取设备信息
    fetchDeviceInfo() {
      this.loading = true;
      this.error = false;

      // 模拟API请求，实际应用中应该调用后端接口
      setTimeout(() => {
        // 根据扫码结果模拟不同设备类型的返回
        if (this.code.includes('CYL')) {
          // 气瓶信息 - 随机选择一种气瓶规格
          const specifications = ['5kg', '15kg', '50kg'];
          const spec = specifications[Math.floor(Math.random() * specifications.length)];

          // 生成使用天数
          const usageDays = Math.floor(Math.random() * 90) + 1;

          this.deviceInfo = {
            type: 'cylinder',
            id: this.code,
            modelName: '液化石油气钢瓶',
            specification: spec,
            startDate: this.formatDate(this.randomDate(90)),
            usageDays: usageDays,
            status: Math.random() > 0.2 ? '正常' : '需检查'
          };
        } else if (this.code.includes('ALM')) {
          // 报警器信息
          const alarmTypes = ['家用燃气报警器', '商用燃气报警器', '便携式报警器'];

          // 生成使用天数
          const usageDays = Math.floor(Math.random() * 365) + 1;

          // 随机选择用户类型
          const userType = this.alarmUserTypes[Math.floor(Math.random() * this.alarmUserTypes.length)].value;

          this.deviceInfo = {
            type: 'alarm',
            id: this.code,
            modelName: alarmTypes[Math.floor(Math.random() * alarmTypes.length)],
            startDate: this.formatDate(this.randomDate(180)),
            usageDays: usageDays,
            status: Math.random() > 0.1 ? '正常' : '需维修',
            userType: userType
          };
        } else {
          // 无效的二维码
          this.error = true;
          this.errorMsg = '无效的设备二维码，请联系客服';
        }

        this.loading = false;
      }, 1500);
    },

    // 确认归还
    confirmReturn() {
      // 显示确认对话框
      uni.showModal({
        title: '确认归还',
        content: `您确定要归还该${this.deviceInfo.type === 'cylinder' ? '气瓶' : '报警器'}吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '正在处理'
            });

            // 模拟API请求
            setTimeout(() => {
              uni.hideLoading();
              
              // 模拟结果，90%成功率
              if (Math.random() < 0.9) {
                // 成功
                let message = '归还成功';
                
                // 显示简短成功信息
                uni.showToast({
                  title: message,
                  icon: 'success',
                  duration: 2000
                });

                // 返回上一页
                setTimeout(() => {
                  uni.navigateBack();
                }, 2000);
              } else {
                // 失败，显示错误
                uni.showModal({
                  title: '归还失败',
                  content: '系统处理异常，请稍后重试或联系客服',
                  showCancel: false
                });
              }
            }, 2000);
          }
        }
      });
    },

    // 取消归还
    cancel() {
      uni.navigateBack();
    },

    // 重新扫码
    scanAgain() {
      uni.scanCode({
        success: (res) => {
          this.code = res.result;
          this.fetchDeviceInfo();
        },
        fail: () => {
          uni.showToast({
            title: '扫码失败',
            icon: 'none'
          });
        }
      });
    },

    // 生成随机日期（最近n天内）
    randomDate(days) {
      const today = new Date();
      const pastDate = new Date();
      pastDate.setDate(today.getDate() - Math.floor(Math.random() * days));
      return pastDate;
    },

    // 格式化日期为YYYY-MM-DD
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 获取报警器用户类型名称
    getAlarmUserTypeName(value) {
      const userType = this.alarmUserTypes.find(type => type.value === value);
      return userType ? userType.name : '未知类型';
    }
  }
}
</script>

<style scoped>
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-text, .error-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.error-text {
  margin-bottom: 30rpx;
}

.info-container {
  position: relative;
}

.device-type-badge {
  position: absolute;
  top: -15rpx;
  right: 30rpx;
  padding: 10rpx 30rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: white;
  z-index: 1;
  display: flex;
  align-items: center;
}

.device-type-badge text {
  margin-left: 6rpx;
}

.device-type-badge.cylinder {
  background-color: #2979ff;
}

.device-type-badge.alarm {
  background-color: #fa3534;
}

.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}

.info-grid {
  display: flex;
  flex-wrap: wrap;
}

.info-item {
  width: 50%;
  margin-bottom: 20rpx;
}

.label {
  font-size: 26rpx;
  color: #999;
  display: block;
  margin-bottom: 8rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.highlight {
  color: #2979ff;
  font-weight: bold;
}

.status-tag {
  display: inline-block;
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.status-tag.normal {
  background-color: #19be6b;
}

.status-tag.warning {
  background-color: #fa3534;
}



.note {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  background-color: rgba(250, 53, 52, 0.1);
  padding: 16rpx;
  border-radius: 8rpx;
}

.note text {
  font-size: 24rpx;
  color: #fa3534;
  margin-left: 10rpx;
}



.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
}

.action-button {
  width: 45%;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 45rpx;
  font-size: 32rpx;
  text-align: center;
  font-weight: 500;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.action-button.primary {
  background-color: #2979ff;
  color: white;
}

.action-button.cancel {
  background-color: #f5f5f5;
  color: #666;
}
</style> 